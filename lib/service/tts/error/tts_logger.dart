import 'package:dasso_reader/service/tts/error/tts_context.dart';
import 'package:dasso_reader/service/tts/error/tts_exceptions.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Enhanced TTS logging system with context awareness and conflict detection
/// 
/// This logger provides:
/// 1. Context-aware logging for Dictionary vs Continuous Reading operations
/// 2. Conflict detection between different TTS contexts
/// 3. Performance monitoring and metrics collection
/// 4. Structured logging for better debugging
class TtsLogger {
  static final TtsLogger _instance = TtsLogger._internal();
  factory TtsLogger() => _instance;
  TtsLogger._internal();

  /// Track active operations by context
  final Map<TtsContext, TtsOperation?> _activeOperations = {};

  /// Track engine usage by context
  final Map<TtsContext, TtsEngineType?> _contextEngineUsage = {};

  /// Performance metrics collection
  final Map<String, List<Duration>> _performanceMetrics = {};

  /// Error frequency tracking
  final Map<String, int> _errorCounts = {};

  /// Maximum number of performance samples to keep
  static const int _maxPerformanceSamples = 100;

  /// Log the start of a TTS operation
  void logOperationStart(
    TtsContext context,
    TtsEngineType engine,
    String operation, {
    Map<String, dynamic>? additionalContext,
  }) {
    final operationId = _generateOperationId();
    final ttsOperation = TtsOperation(
      id: operationId,
      context: context,
      engine: engine,
      operation: operation,
      startTime: DateTime.now(),
      additionalContext: additionalContext ?? {},
    );

    _activeOperations[context] = ttsOperation;
    _contextEngineUsage[context] = engine;

    // Check for potential conflicts
    _checkForConflicts(context, engine);

    // Log the operation start
    final logMessage = _formatOperationStartMessage(ttsOperation);
    AnxLog.info(logMessage);

    // Log additional context if provided
    if (additionalContext != null && additionalContext.isNotEmpty) {
      AnxLog.info('[TTS] ${context.displayName} Context: $additionalContext');
    }
  }

  /// Log the successful completion of a TTS operation
  void logOperationSuccess(
    TtsContext context,
    String operation, {
    Map<String, dynamic>? metrics,
  }) {
    final activeOperation = _activeOperations[context];
    if (activeOperation == null) {
      AnxLog.warning('[TTS] ${context.displayName}: No active operation found for success logging');
      return;
    }

    final duration = DateTime.now().difference(activeOperation.startTime);
    
    // Record performance metrics
    _recordPerformanceMetric('${context.name}_${operation}', duration);

    // Log success
    final logMessage = _formatOperationSuccessMessage(activeOperation, duration, metrics);
    AnxLog.info(logMessage);

    // Clear active operation
    _activeOperations[context] = null;
    _contextEngineUsage[context] = null;
  }

  /// Log a TTS operation failure
  void logOperationFailure(
    TtsContext context,
    String operation,
    TtsException error,
  ) {
    final activeOperation = _activeOperations[context];
    final duration = activeOperation != null 
        ? DateTime.now().difference(activeOperation.startTime)
        : null;

    // Track error frequency
    final errorKey = '${context.name}_${error.type.name}';
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;

    // Log the error with context
    final logMessage = _formatOperationFailureMessage(activeOperation, error, duration);
    AnxLog.severe(logMessage, error.originalError, error.stackTrace);

    // Clear active operation
    _activeOperations[context] = null;
    _contextEngineUsage[context] = null;
  }

  /// Log a state change in TTS system
  void logStateChange(
    TtsContext context,
    TtsEngineType engine,
    String fromState,
    String toState, {
    String? reason,
  }) {
    final message = StringBuffer();
    message.write('[TTS] ${context.displayName} - ${engine.displayName}: ');
    message.write('State changed from $fromState to $toState');
    
    if (reason != null) {
      message.write(' (Reason: $reason)');
    }

    AnxLog.info(message.toString());
  }

  /// Log a conflict detection event
  void logConflictDetected(
    TtsContext requestingContext,
    TtsEngineType requestedEngine,
    TtsContext conflictingContext,
    TtsEngineType conflictingEngine,
  ) {
    final message = '[TTS] CONFLICT DETECTED: '
        '${requestingContext.displayName} requesting ${requestedEngine.displayName} '
        'but ${conflictingContext.displayName} is using ${conflictingEngine.displayName}';
    
    AnxLog.warning(message);
  }

  /// Log a fallback operation
  void logFallback(
    TtsContext context,
    TtsEngineType fromEngine,
    TtsEngineType toEngine,
    String reason,
  ) {
    final message = '[TTS] ${context.displayName}: '
        'Falling back from ${fromEngine.displayName} to ${toEngine.displayName} '
        '(Reason: $reason)';
    
    AnxLog.info(message);
  }

  /// Get performance statistics for a specific operation
  Map<String, dynamic> getPerformanceStats(String operationKey) {
    final samples = _performanceMetrics[operationKey] ?? [];
    if (samples.isEmpty) {
      return {'count': 0};
    }

    final durations = samples.map((d) => d.inMilliseconds).toList();
    durations.sort();

    return {
      'count': samples.length,
      'average_ms': durations.reduce((a, b) => a + b) / durations.length,
      'median_ms': durations[durations.length ~/ 2],
      'min_ms': durations.first,
      'max_ms': durations.last,
      'p95_ms': durations[(durations.length * 0.95).floor()],
    };
  }

  /// Get error frequency statistics
  Map<String, int> getErrorStats() {
    return Map.from(_errorCounts);
  }

  /// Get current active operations (for debugging)
  Map<TtsContext, TtsOperation?> getActiveOperations() {
    return Map.from(_activeOperations);
  }

  /// Check for potential conflicts between TTS contexts
  void _checkForConflicts(TtsContext requestingContext, TtsEngineType requestedEngine) {
    for (final entry in _contextEngineUsage.entries) {
      final activeContext = entry.key;
      final activeEngine = entry.value;

      if (activeContext == requestingContext || activeEngine == null) {
        continue; // Same context or no active engine
      }

      // Check for engine conflicts
      if (activeEngine == requestedEngine) {
        logConflictDetected(requestingContext, requestedEngine, activeContext, activeEngine);
      }

      // Check for forbidden engine usage
      if (requestingContext.forbiddenEngines.contains(requestedEngine)) {
        AnxLog.warning('[TTS] ${requestingContext.displayName} attempting to use forbidden engine: ${requestedEngine.displayName}');
      }
    }
  }

  /// Record a performance metric sample
  void _recordPerformanceMetric(String key, Duration duration) {
    _performanceMetrics.putIfAbsent(key, () => <Duration>[]);
    final samples = _performanceMetrics[key]!;
    
    samples.add(duration);
    
    // Keep only the most recent samples
    if (samples.length > _maxPerformanceSamples) {
      samples.removeAt(0);
    }
  }

  /// Generate a unique operation ID
  String _generateOperationId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${_activeOperations.length}';
  }

  /// Format operation start log message
  String _formatOperationStartMessage(TtsOperation operation) {
    return '[TTS] ${operation.context.displayName} - ${operation.engine.displayName}: '
        'Starting ${operation.operation} (ID: ${operation.id})';
  }

  /// Format operation success log message
  String _formatOperationSuccessMessage(
    TtsOperation operation,
    Duration duration,
    Map<String, dynamic>? metrics,
  ) {
    final message = StringBuffer();
    message.write('[TTS] ${operation.context.displayName} - ${operation.engine.displayName}: ');
    message.write('${operation.operation} completed in ${duration.inMilliseconds}ms');
    message.write(' (ID: ${operation.id})');

    if (metrics != null && metrics.isNotEmpty) {
      message.write(' | Metrics: $metrics');
    }

    return message.toString();
  }

  /// Format operation failure log message
  String _formatOperationFailureMessage(
    TtsOperation? operation,
    TtsException error,
    Duration? duration,
  ) {
    final message = StringBuffer();
    
    if (operation != null) {
      message.write('[TTS] ${operation.context.displayName} - ${operation.engine.displayName}: ');
      message.write('${operation.operation} failed');
      if (duration != null) {
        message.write(' after ${duration.inMilliseconds}ms');
      }
      message.write(' (ID: ${operation.id})');
    } else {
      message.write('[TTS] ${error.context.displayName} - ${error.failedEngine.displayName}: Operation failed');
    }

    message.write(' | Error: ${error.type.name} - ${error.message}');
    
    if (error.technicalDetails != null) {
      message.write(' | Details: ${error.technicalDetails}');
    }

    return message.toString();
  }
}

/// Represents an active TTS operation
class TtsOperation {
  final String id;
  final TtsContext context;
  final TtsEngineType engine;
  final String operation;
  final DateTime startTime;
  final Map<String, dynamic> additionalContext;

  const TtsOperation({
    required this.id,
    required this.context,
    required this.engine,
    required this.operation,
    required this.startTime,
    required this.additionalContext,
  });

  Duration get elapsed => DateTime.now().difference(startTime);

  @override
  String toString() {
    return 'TtsOperation(id: $id, context: ${context.name}, engine: ${engine.name}, operation: $operation, elapsed: ${elapsed.inMilliseconds}ms)';
  }
}
