/// TTS Context Classification System
/// 
/// This system provides context awareness for TTS operations to prevent
/// conflicts between Dictionary pronunciation and Continuous reading TTS engines.
/// 
/// Key Design Principles:
/// 1. Dictionary TTS and Continuous Reading TTS must never use the same engine simultaneously
/// 2. Each TTS operation must be tagged with its context
/// 3. Fallback chains are context-specific and conflict-aware
/// 4. Resource coordination prevents simultaneous engine usage

/// Defines the context in which TTS operations are performed
/// This is critical for preventing conflicts between different TTS use cases
enum TtsContext {
  /// Dictionary word/phrase pronunciation
  /// - Primary: System TTS (flutter_tts)
  /// - Fallback: Google Translate TTS (never Edge TTS)
  /// - Characteristics: Short text, immediate playback, Chinese pronunciation focus
  dictionaryPronunciation,

  /// Continuous chapter/book reading
  /// - Primary: Edge TTS with voice models
  /// - Fallback: System TTS (only if dictionary not using it)
  /// - Characteristics: Long text, background playback, narrative reading
  continuousReading,
}

/// Available TTS engine types in the system
enum TtsEngineType {
  /// System TTS using flutter_tts package
  /// - Used by: Dictionary (primary), Continuous Reading (fallback)
  /// - Characteristics: Offline, platform-dependent quality
  systemTts,

  /// Microsoft Edge TTS using WebSocket API
  /// - Used by: Continuous Reading (primary)
  /// - Characteristics: Online, high-quality voices, requires network
  edgeTts,

  /// Google Translate TTS using HTTP API
  /// - Used by: Dictionary (fallback only)
  /// - Characteristics: Online, reliable, doesn't conflict with other engines
  googleTranslateTts,
}

/// Detailed error classification for TTS operations
enum TtsErrorType {
  // Network-related errors (primarily affect Edge TTS and Google Translate TTS)
  networkConnection,
  networkTimeout,
  networkUnauthorized,
  networkServiceUnavailable,

  // Platform-specific errors (primarily affect System TTS)
  platformEngineUnavailable,
  platformVoiceUnavailable,
  platformPermissionDenied,
  platformConfigurationInvalid,
  platformLanguageUnsupported,

  // Audio-related errors (affect all engines)
  audioPlayerInitialization,
  audioPlaybackFailed,
  audioFormatUnsupported,
  audioDeviceUnavailable,
  audioSessionConflict,

  // State management errors
  stateInvalidTransition,
  stateConcurrentOperation,
  stateResourceBusy,
  stateEngineNotInitialized,

  // Content-related errors
  contentEmpty,
  contentTooLong,
  contentInvalidFormat,
  contentUnsupportedLanguage,

  // Configuration errors
  configurationInvalid,
  configurationMissing,
  configurationConflict,

  // Resource conflicts (new for dual-TTS architecture)
  resourceEngineInUse,
  resourceContextConflict,
  resourceUnavailable,

  // Unknown/Generic errors
  unknown,
}

/// Error severity levels for appropriate user feedback and logging
enum TtsErrorSeverity {
  /// Low severity - subtle notification, operation can continue with degraded functionality
  /// Examples: Voice model fallback, minor configuration adjustments
  low,

  /// Medium severity - user notification with retry option, affects current operation
  /// Examples: Network timeouts, temporary engine unavailability
  medium,

  /// High severity - prominent user feedback, significant functionality impact
  /// Examples: Engine initialization failure, audio device unavailable
  high,

  /// Critical severity - system-level failure, requires immediate attention
  /// Examples: All engines failed, resource conflicts, configuration corruption
  critical,
}

/// Extension methods for TtsContext to provide context-specific behavior
extension TtsContextExtension on TtsContext {
  /// Get the primary TTS engine for this context
  TtsEngineType get primaryEngine {
    switch (this) {
      case TtsContext.dictionaryPronunciation:
        return TtsEngineType.systemTts;
      case TtsContext.continuousReading:
        return TtsEngineType.edgeTts;
    }
  }

  /// Get the safe fallback engines for this context (in order of preference)
  List<TtsEngineType> get safeFallbackEngines {
    switch (this) {
      case TtsContext.dictionaryPronunciation:
        // Dictionary NEVER falls back to Edge TTS to avoid conflicts
        return [TtsEngineType.googleTranslateTts];
      case TtsContext.continuousReading:
        // Continuous reading can fall back to System TTS if dictionary isn't using it
        return [TtsEngineType.systemTts];
    }
  }

  /// Get forbidden engines for this context (to prevent conflicts)
  List<TtsEngineType> get forbiddenEngines {
    switch (this) {
      case TtsContext.dictionaryPronunciation:
        // Dictionary must never use Edge TTS to avoid conflicts with continuous reading
        return [TtsEngineType.edgeTts];
      case TtsContext.continuousReading:
        // Continuous reading should avoid Google Translate TTS (reserved for dictionary fallback)
        return [TtsEngineType.googleTranslateTts];
    }
  }

  /// Get human-readable name for this context
  String get displayName {
    switch (this) {
      case TtsContext.dictionaryPronunciation:
        return 'Dictionary Pronunciation';
      case TtsContext.continuousReading:
        return 'Continuous Reading';
    }
  }

  /// Get context-specific configuration preferences
  Map<String, dynamic> get defaultConfiguration {
    switch (this) {
      case TtsContext.dictionaryPronunciation:
        return {
          'language': 'zh-CN',
          'rate': 0.5, // Slower for pronunciation clarity
          'pitch': 1.0,
          'volume': 1.0,
          'awaitCompletion': true,
        };
      case TtsContext.continuousReading:
        return {
          'language': 'auto', // Based on content or user preference
          'rate': 0.8, // Normal reading speed
          'pitch': 1.0,
          'volume': 1.0,
          'awaitCompletion': false, // For continuous playback
        };
    }
  }
}

/// Extension methods for TtsEngineType to provide engine-specific information
extension TtsEngineTypeExtension on TtsEngineType {
  /// Check if this engine requires network connectivity
  bool get requiresNetwork {
    switch (this) {
      case TtsEngineType.systemTts:
        return false;
      case TtsEngineType.edgeTts:
      case TtsEngineType.googleTranslateTts:
        return true;
    }
  }

  /// Check if this engine supports offline operation
  bool get supportsOffline {
    return !requiresNetwork;
  }

  /// Get human-readable name for this engine
  String get displayName {
    switch (this) {
      case TtsEngineType.systemTts:
        return 'System TTS';
      case TtsEngineType.edgeTts:
        return 'Edge TTS';
      case TtsEngineType.googleTranslateTts:
        return 'Google Translate TTS';
    }
  }

  /// Get typical use cases for this engine
  List<TtsContext> get typicalContexts {
    switch (this) {
      case TtsEngineType.systemTts:
        return [TtsContext.dictionaryPronunciation, TtsContext.continuousReading];
      case TtsEngineType.edgeTts:
        return [TtsContext.continuousReading];
      case TtsEngineType.googleTranslateTts:
        return [TtsContext.dictionaryPronunciation];
    }
  }
}

/// Extension methods for TtsErrorType to provide error-specific behavior
extension TtsErrorTypeExtension on TtsErrorType {
  /// Get the default severity for this error type
  TtsErrorSeverity get defaultSeverity {
    switch (this) {
      case TtsErrorType.networkTimeout:
      case TtsErrorType.platformVoiceUnavailable:
      case TtsErrorType.contentEmpty:
        return TtsErrorSeverity.low;

      case TtsErrorType.networkConnection:
      case TtsErrorType.platformEngineUnavailable:
      case TtsErrorType.audioPlaybackFailed:
      case TtsErrorType.stateResourceBusy:
        return TtsErrorSeverity.medium;

      case TtsErrorType.platformPermissionDenied:
      case TtsErrorType.audioDeviceUnavailable:
      case TtsErrorType.resourceEngineInUse:
      case TtsErrorType.resourceContextConflict:
        return TtsErrorSeverity.high;

      case TtsErrorType.configurationCorruption:
      case TtsErrorType.stateInvalidTransition:
      case TtsErrorType.unknown:
        return TtsErrorSeverity.critical;

      default:
        return TtsErrorSeverity.medium;
    }
  }

  /// Check if this error type typically allows retry
  bool get allowsRetry {
    switch (this) {
      case TtsErrorType.networkTimeout:
      case TtsErrorType.networkConnection:
      case TtsErrorType.stateResourceBusy:
      case TtsErrorType.audioSessionConflict:
        return true;

      case TtsErrorType.platformPermissionDenied:
      case TtsErrorType.platformEngineUnavailable:
      case TtsErrorType.contentInvalidFormat:
      case TtsErrorType.configurationInvalid:
        return false;

      default:
        return true;
    }
  }

  /// Get suggested retry delay for this error type
  Duration? get retryDelay {
    if (!allowsRetry) return null;

    switch (this) {
      case TtsErrorType.networkTimeout:
        return const Duration(seconds: 3);
      case TtsErrorType.networkConnection:
        return const Duration(seconds: 5);
      case TtsErrorType.stateResourceBusy:
        return const Duration(milliseconds: 500);
      case TtsErrorType.audioSessionConflict:
        return const Duration(seconds: 1);
      default:
        return const Duration(seconds: 2);
    }
  }
}
