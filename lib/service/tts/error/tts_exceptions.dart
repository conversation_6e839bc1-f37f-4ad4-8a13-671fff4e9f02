import 'package:dasso_reader/service/tts/error/tts_context.dart';

/// Base class for all TTS-related exceptions
/// 
/// This exception system is designed to be context-aware and prevent conflicts
/// between Dictionary pronunciation and Continuous reading TTS systems.
abstract class TtsException implements Exception {
  /// The context in which this error occurred
  final TtsContext context;

  /// The TTS engine that failed
  final TtsEngineType failedEngine;

  /// The specific type of error
  final TtsErrorType type;

  /// The severity of this error
  final TtsErrorSeverity severity;

  /// Human-readable error message for developers
  final String message;

  /// Technical details for debugging (not shown to users)
  final String? technicalDetails;

  /// The original error that caused this exception (if any)
  final Object? originalError;

  /// Stack trace from the original error
  final StackTrace? stackTrace;

  /// When this error occurred
  final DateTime timestamp;

  /// Additional context information for debugging
  final Map<String, dynamic>? context;

  const TtsException({
    required this.context,
    required this.failedEngine,
    required this.type,
    required this.message,
    TtsErrorSeverity? severity,
    this.technicalDetails,
    this.originalError,
    this.stackTrace,
    DateTime? timestamp,
    this.context,
  })  : severity = severity ?? type.defaultSeverity,
        timestamp = timestamp ?? DateTime.now();

  /// Get user-friendly error message (localized)
  String get userFriendlyMessage;

  /// Get detailed log message for developers
  String get logMessage {
    final buffer = StringBuffer();
    buffer.write('[TTS] ${context.displayName} - ${failedEngine.displayName}: ');
    buffer.write('${type.name} - $message');

    if (technicalDetails != null) {
      buffer.write(' | Details: $technicalDetails');
    }

    if (context != null && context!.isNotEmpty) {
      buffer.write(' | Context: $context');
    }

    return buffer.toString();
  }

  /// Whether this error should trigger a retry attempt
  bool get shouldRetry => type.allowsRetry;

  /// Suggested delay before retry (null if no retry recommended)
  Duration? get retryDelay => type.retryDelay;

  /// Whether this error should trigger a fallback to another engine
  bool get shouldFallback => _shouldFallbackForContext();

  /// Get safe fallback engines for this error context
  List<TtsEngineType> get safeFallbackEngines {
    if (!shouldFallback) return [];
    
    return context.safeFallbackEngines
        .where((engine) => engine != failedEngine)
        .toList();
  }

  /// Check if fallback is appropriate for this context and error
  bool _shouldFallbackForContext() {
    // Don't fallback for configuration or permission errors
    if (type == TtsErrorType.platformPermissionDenied ||
        type == TtsErrorType.configurationInvalid ||
        type == TtsErrorType.configurationMissing) {
      return false;
    }

    // Don't fallback if no safe alternatives exist
    if (context.safeFallbackEngines.isEmpty) {
      return false;
    }

    // Don't fallback for resource conflicts (need coordination instead)
    if (type == TtsErrorType.resourceEngineInUse ||
        type == TtsErrorType.resourceContextConflict) {
      return false;
    }

    return true;
  }

  @override
  String toString() {
    return 'TtsException: $logMessage';
  }
}

/// Network-related TTS errors (primarily Edge TTS and Google Translate TTS)
class TtsNetworkException extends TtsException {
  const TtsNetworkException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.networkConnection:
        return context == TtsContext.dictionaryPronunciation
            ? 'Unable to connect for pronunciation. Check your internet connection.'
            : 'Unable to connect to reading service. Check your internet connection.';

      case TtsErrorType.networkTimeout:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation service is taking too long to respond.'
            : 'Reading service is taking too long to respond.';

      case TtsErrorType.networkServiceUnavailable:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation service is temporarily unavailable.'
            : 'Reading service is temporarily unavailable.';

      default:
        return 'Network error occurred. Please check your connection.';
    }
  }
}

/// Platform-specific TTS errors (primarily System TTS)
class TtsPlatformException extends TtsException {
  const TtsPlatformException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.platformEngineUnavailable:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation engine is not available on this device.'
            : 'Text-to-speech engine is not available on this device.';

      case TtsErrorType.platformVoiceUnavailable:
        return 'Selected voice is not available. Using default voice.';

      case TtsErrorType.platformPermissionDenied:
        return 'Audio permission is required for text-to-speech functionality.';

      case TtsErrorType.platformLanguageUnsupported:
        return context == TtsContext.dictionaryPronunciation
            ? 'Chinese pronunciation is not supported on this device.'
            : 'Selected language is not supported for reading.';

      default:
        return 'Platform-specific error occurred with text-to-speech.';
    }
  }
}

/// Audio-related TTS errors (affects all engines)
class TtsAudioException extends TtsException {
  const TtsAudioException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.audioPlayerInitialization:
        return 'Audio player could not be initialized. Please try again.';

      case TtsErrorType.audioPlaybackFailed:
        return context == TtsContext.dictionaryPronunciation
            ? 'Could not play pronunciation. Please check your audio settings.'
            : 'Audio playback failed. Please check your audio settings.';

      case TtsErrorType.audioDeviceUnavailable:
        return 'Audio device is not available. Please check your audio settings.';

      case TtsErrorType.audioSessionConflict:
        return 'Audio session conflict detected. Please try again.';

      default:
        return 'Audio error occurred. Please check your audio settings.';
    }
  }
}

/// Resource conflict errors (specific to dual-TTS architecture)
class TtsResourceException extends TtsException {
  const TtsResourceException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          severity: TtsErrorSeverity.high, // Resource conflicts are always high severity
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.resourceEngineInUse:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation is temporarily unavailable. Please try again.'
            : 'Reading service is busy. Please try again.';

      case TtsErrorType.resourceContextConflict:
        return 'Text-to-speech conflict detected. Please try again.';

      case TtsErrorType.resourceUnavailable:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation service is temporarily unavailable.'
            : 'Reading service is temporarily unavailable.';

      default:
        return 'Resource conflict occurred. Please try again.';
    }
  }

  @override
  bool get shouldFallback => false; // Resource conflicts need coordination, not fallback
}

/// Content-related TTS errors
class TtsContentException extends TtsException {
  const TtsContentException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.contentEmpty:
        return context == TtsContext.dictionaryPronunciation
            ? 'No text selected for pronunciation.'
            : 'No content available for reading.';

      case TtsErrorType.contentTooLong:
        return context == TtsContext.dictionaryPronunciation
            ? 'Selected text is too long for pronunciation.'
            : 'Content is too long for text-to-speech processing.';

      case TtsErrorType.contentInvalidFormat:
        return 'Content format is not supported for text-to-speech.';

      case TtsErrorType.contentUnsupportedLanguage:
        return context == TtsContext.dictionaryPronunciation
            ? 'Language not supported for pronunciation.'
            : 'Language not supported for reading.';

      default:
        return 'Content error occurred with text-to-speech.';
    }
  }
}

/// Configuration-related TTS errors
class TtsConfigurationException extends TtsException {
  const TtsConfigurationException({
    required TtsContext context,
    required TtsEngineType failedEngine,
    required TtsErrorType type,
    required String message,
    String? technicalDetails,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalContext,
  }) : super(
          context: context,
          failedEngine: failedEngine,
          type: type,
          message: message,
          technicalDetails: technicalDetails,
          originalError: originalError,
          stackTrace: stackTrace,
          context: additionalContext,
        );

  @override
  String get userFriendlyMessage {
    switch (type) {
      case TtsErrorType.configurationInvalid:
        return 'Text-to-speech configuration is invalid. Please check your settings.';

      case TtsErrorType.configurationMissing:
        return 'Text-to-speech configuration is missing. Please check your settings.';

      case TtsErrorType.configurationConflict:
        return 'Text-to-speech configuration conflict detected.';

      default:
        return 'Configuration error occurred. Please check your settings.';
    }
  }

  @override
  bool get shouldFallback => false; // Configuration errors need fixing, not fallback
}
